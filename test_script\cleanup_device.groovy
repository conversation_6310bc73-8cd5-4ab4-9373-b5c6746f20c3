/*
 * @Author: javen
 * @Date: 2025-07-11 17:30:00
 * @LastEditors: javen
 * @LastEditTime: 2025-07-11 17:30:00
 * @Description: 设备清理工具脚本 - 专门用于清理 UiAutomationService 冲突
 */
import org.cloud.sonic.agent.bridge.android.AndroidDeviceBridgeTool
import org.cloud.sonic.agent.tests.LogUtil
import org.cloud.sonic.agent.common.interfaces.StepType

/**
 * 执行本地PC上的command命令
 * @param args command命令参数
 * @return 命令执行结果
 */
def exec_command(String args) {
    try {
        String command = "${args}"
        Process process = command.execute()
        process.waitFor()

        String output = process.inputStream.text
        String error = process.errorStream.text

        if (error && !error.trim().isEmpty()) {
            androidStepHandler.log.sendStepLog(StepType.WARN, "终端命令警告: ${error}", "")
        }

        return output.trim()
    } catch (Exception e) {
        androidStepHandler.log.sendStepLog(StepType.ERROR, "执行终端命令失败: ${args}", e.toString())
        return ""
    }
}

/**
 * 完全清理设备自动化环境
 */
def deepCleanDevice() {
    LogUtil log = androidStepHandler.log
    def iDevice = androidStepHandler.iDevice
    
    log.sendStepLog(StepType.INFO, "开始深度清理设备自动化环境...", "")
    
    try {
        // 1. 关闭所有可能的驱动连接
        try {
            androidStepHandler.getAndroidDriver().closeDriver()
        } catch (Exception e) {
            log.sendStepLog(StepType.INFO, "驱动已关闭或不存在", "")
        }
        
        // 2. 杀死设备上所有相关进程
        def processesToKill = [
            "monkey", "fastbot", "app_process", "uiautomator", 
            "com.android.commands.monkey", "accessibility"
        ]
        
        processesToKill.each { processName ->
            try {
                AndroidDeviceBridgeTool.executeCommand(iDevice, "pkill -f ${processName}")
                AndroidDeviceBridgeTool.executeCommand(iDevice, "killall ${processName}")
            } catch (Exception e) {
                // 忽略进程不存在的错误
            }
        }
        
        // 3. 强制停止所有自动化相关服务
        AndroidDeviceBridgeTool.executeCommand(iDevice, "am force-stop com.android.commands.monkey")
        
        // 4. 重置 accessibility 设置
        AndroidDeviceBridgeTool.executeCommand(iDevice, "settings put secure accessibility_enabled 0")
        Thread.sleep(2000)
        AndroidDeviceBridgeTool.executeCommand(iDevice, "settings put secure accessibility_enabled 1")
        
        // 5. 清理临时文件
        AndroidDeviceBridgeTool.executeCommand(iDevice, "rm -rf /data/local/tmp/uiautomator*")
        AndroidDeviceBridgeTool.executeCommand(iDevice, "rm -rf /data/local/tmp/monkey*")
        
        // 6. 重启 ADB 连接
        log.sendStepLog(StepType.INFO, "重启 ADB 连接...", "")
        exec_command("adb kill-server")
        Thread.sleep(3000)
        exec_command("adb start-server")
        Thread.sleep(5000)
        
        // 7. 等待设备稳定
        log.sendStepLog(StepType.INFO, "等待设备稳定...", "")
        Thread.sleep(5000)
        
        log.sendStepLog(StepType.PASS, "设备深度清理完成", "")
        
    } catch (Exception e) {
        log.sendStepLog(StepType.ERROR, "深度清理设备时出现异常", e.toString())
        throw e
    }
}

/**
 * 检查设备状态
 */
def checkDeviceStatus() {
    LogUtil log = androidStepHandler.log
    def iDevice = androidStepHandler.iDevice
    
    log.sendStepLog(StepType.INFO, "检查设备状态...", "")
    
    try {
        // 检查设备连接
        String deviceList = exec_command("adb devices")
        log.sendStepLog(StepType.INFO, "设备列表: ${deviceList}", "")
        
        // 检查运行中的进程
        String processes = AndroidDeviceBridgeTool.executeCommand(iDevice, "ps | grep -E '(monkey|fastbot|uiautomator)'")
        if (processes && !processes.trim().isEmpty()) {
            log.sendStepLog(StepType.WARN, "发现运行中的自动化进程: ${processes}", "")
        } else {
            log.sendStepLog(StepType.PASS, "没有发现冲突的自动化进程", "")
        }
        
        // 检查 accessibility 状态
        String accessibilityStatus = AndroidDeviceBridgeTool.executeCommand(iDevice, "settings get secure accessibility_enabled")
        log.sendStepLog(StepType.INFO, "Accessibility 状态: ${accessibilityStatus}", "")
        
    } catch (Exception e) {
        log.sendStepLog(StepType.ERROR, "检查设备状态时出现异常", e.toString())
    }
}

// 主执行函数
def main() {
    LogUtil log = androidStepHandler.log
    log.sendStepLog(StepType.INFO, "=== 开始设备清理流程 ===", "")
    
    try {
        checkDeviceStatus()
        deepCleanDevice()
        checkDeviceStatus()
        
        log.sendStepLog(StepType.PASS, "=== 设备清理流程完成 ===", "")
        
    } catch (Exception e) {
        log.sendStepLog(StepType.ERROR, "设备清理流程失败", e.toString())
        throw e
    }
}

// 执行清理
main()
