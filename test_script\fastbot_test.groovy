/*
 * @Author: javen
 * @Date: 2025-07-04 10:00:00
 * @LastEditors: javen
 * @LastEditTime: 2025-07-04 10:00:00
 * @Description: Fastbot测试脚本
 */
import org.cloud.sonic.agent.bridge.android.AndroidDeviceBridgeTool
import org.cloud.sonic.agent.tests.LogUtil
import org.cloud.sonic.agent.common.interfaces.StepType
import java.util.*;
import com.android.ddmlib.IShellOutputReceiver
import com.android.ddmlib.SyncService
import java.util.concurrent.TimeUnit;



/**
 * 获取设备品牌，如：OPPO
 * @return 设备品牌
 */
def getProductBrand() {
    return AndroidDeviceBridgeTool.getProperties(androidStepHandler.iDevice, "ro.product.brand") ?: ""
}

/**
 * 获取设备型号，如：MHA-AL00
 * @return 设备型号
 */
def getProductModel() {
    return AndroidDeviceBridgeTool.getProperties(androidStepHandler.iDevice, "ro.product.model") ?: ""
}


/**
 * 执行本地PC上的command命令
 * @param args command命令参数
 * @return 命令执行结果
 */
def exec_command(String args) {
    try {
        String command = "${args}"
        Process process = command.execute()
        process.waitFor()

        String output = process.inputStream.text
        String error = process.errorStream.text

        if (error && !error.trim().isEmpty()) {
            androidStepHandler.log.sendStepLog(StepType.WARN, "终端命令警告: ${error}", "")
        }

        return output.trim()
    } catch (Exception e) {
        androidStepHandler.log.sendStepLog(StepType.ERROR, "执行终端命令失败: ${args}", e.toString())
        return ""
    }
}


/**
 * 清理设备上的自动化服务和相关进程
 */
def cleanupAutomationServices() {
    LogUtil log = androidStepHandler.log
    def iDevice = androidStepHandler.iDevice

    log.sendStepLog(StepType.INFO, "开始清理设备上的自动化服务...", "")

    try {
        // 1. 杀死所有 monkey 相关进程
        AndroidDeviceBridgeTool.executeCommand(iDevice, "pkill -f monkey")
        AndroidDeviceBridgeTool.executeCommand(iDevice, "pkill -f fastbot")
        AndroidDeviceBridgeTool.executeCommand(iDevice, "pkill -f app_process")

        // 2. 清理 UiAutomation 相关进程
        AndroidDeviceBridgeTool.executeCommand(iDevice, "pkill -f uiautomator")

        // 3. 重启 accessibility 服务
        AndroidDeviceBridgeTool.executeCommand(iDevice, "settings put secure accessibility_enabled 0")
        Thread.sleep(1000)
        AndroidDeviceBridgeTool.executeCommand(iDevice, "settings put secure accessibility_enabled 1")

        // 4. 等待服务重启
        Thread.sleep(2000)

        log.sendStepLog(StepType.PASS, "自动化服务清理完成", "")

    } catch (Exception e) {
        log.sendStepLog(StepType.WARN, "清理自动化服务时出现异常", e.toString())
    }
}

def testFastbot(String command){
    // 执行Fastbot命令
        LogUtil log = androidStepHandler.log

        // 先清理可能存在的自动化服务
        cleanupAutomationServices()

        // 关闭现有的驱动
        try {
            androidStepHandler.getAndroidDriver().closeDriver()
        } catch (Exception e) {
            log.sendStepLog(StepType.WARN, "关闭驱动时出现异常", e.toString())
        }

        // 执行 Fastbot 命令
        androidStepHandler.iDevice.executeShellCommand(command,
                        new IShellOutputReceiver() {
                            @Override
                            public void addOutput(byte[] bytes, int i, int i1) {
                                // 日志已重定向到设备文件，此处无需处理
                            }

                            @Override
                            public void flush() {
                            }

                            @Override
                            public boolean isCancelled() {
                                return false;
                            }
                        }, 0, TimeUnit.MILLISECONDS);

        // 重新启动 UIA 服务器和驱动
        try {
            int port = AndroidDeviceBridgeTool.startUiaServer(androidStepHandler.iDevice);
            androidStepHandler.startAndroidDriver(androidStepHandler.iDevice, port)
        } catch (Exception e) {
            log.sendStepLog(StepType.WARN, "重启驱动时出现异常", e.toString())
        }
}



def checkAndPushFiles() {
    // 首先强制清理环境
    cleanupAutomationServices()

    int port = AndroidDeviceBridgeTool.startUiaServer(androidStepHandler.iDevice);
    androidStepHandler.startAndroidDriver(androidStepHandler.iDevice, port)
    // Fastbot 配置参数
    def outputDirectory = "/sdcard/test/log/crash" // 日志输出目录
    def throttle = 800 // 事件概率
    def runningMinutes = 1440 // 运行时间 单位：分钟
    // 检查并同步Fastbot所需文件
    LogUtil log = androidStepHandler.log
    def iDevice = androidStepHandler.iDevice
    String localPath = "/root/ftp_test/sonic_data/fastbot/"
    String remotePath = "/sdcard/"
    def filesToSync = ["fastbot-thirdpart.jar", "framework.jar", "monkeyq.jar"]
    def dirsToSync = ["libs"]
    def packageName = "com.heytap.music" // 默认包名
    log.sendStepLog(StepType.INFO, "开始检查并同步Fastbot所需文件...", "")

    try {
        // 检查和上传文件
        filesToSync.each { fileName ->
            def checkResult = AndroidDeviceBridgeTool.executeCommand(iDevice, "ls " + remotePath + fileName)
            if (checkResult.contains("No such file or directory")) {
                log.sendStepLog(StepType.INFO, "文件 " + fileName + " 不存在，开始上传...", "")
                iDevice.pushFile(localPath + fileName, remotePath + fileName)
                log.sendStepLog(StepType.PASS, "文件 " + fileName + " 上传成功.", "")
            } else {
                log.sendStepLog(StepType.PASS, "文件 " + fileName + " 已存在.", "")
            }
        }

        // 检查和上传目录
        dirsToSync.each { dirName ->
            if (dirName == "libs") {
                log.sendStepLog(StepType.INFO, "开始检查并同步 libs 内容到 /data/local/tmp/ ...", "")
                File localLibsDir = new File(localPath + "libs")
                String remoteTmpPath = "/data/local/tmp"

                if (localLibsDir.isDirectory()) {
                    localLibsDir.eachFileRecurse { file ->
                        String relativePath = file.absolutePath.substring(localLibsDir.absolutePath.length())
                        String remoteFullPath = (remoteTmpPath + relativePath).replace(File.separator, "/")
                        
                        def checkResult = AndroidDeviceBridgeTool.executeCommand(iDevice, "ls " + remoteFullPath)
                        if (checkResult.contains("No such file or directory")) {
                             log.sendStepLog(StepType.INFO, "目标路径 " + remoteFullPath + " 不存在，开始创建或上传...", "")
                            if (file.isDirectory()) {
                                AndroidDeviceBridgeTool.executeCommand(iDevice, "mkdir -p " + remoteFullPath)
                            } else {
                                iDevice.pushFile(file.absolutePath, remoteFullPath)
                            }
                             log.sendStepLog(StepType.PASS, "路径 " + remoteFullPath + " 处理完成.", "")
                        } else {
                            if (!file.isDirectory()) {
                                log.sendStepLog(StepType.PASS, "文件 " + file.name + " 已存在于 " + remoteFullPath, "")
                            }
                        }
                    }
                    log.sendStepLog(StepType.PASS, "libs 内容同步完成.", "")
                }
            }
        }
        
        log.sendStepLog(StepType.PASS, "所有文件和目录均已同步完成", "")

        // 获取包名
        log.sendStepLog(StepType.INFO, "正在启动 '${packageName}' 包名app...", "")


        // --- 执行Fastbot命令 ---
        // 1. 生成日志文件名
        def sdf = new java.text.SimpleDateFormat("yyyyMMddHHmmss")
        sdf.setTimeZone(java.util.TimeZone.getTimeZone("Asia/Shanghai"))
        def timestamp = sdf.format(new Date())
        def logFileName = "fastbot_${timestamp}.log"
        def remoteLogPath = "/sdcard/test/log"
        def remoteLogFile = "${remoteLogPath}/${logFileName}"

        // 2. 创建日志目录
        log.sendStepLog(StepType.INFO, "确保日志目录 ${remoteLogPath} 存在...", "")
        AndroidDeviceBridgeTool.executeCommand(iDevice, "mkdir -p " + remoteLogPath)

        // 3. 构建命令并重定向日志输出
        log.sendStepLog(StepType.INFO, "Fastbot 日志将输出到: ${remoteLogFile}", "")
        String command = "CLASSPATH=/sdcard/monkeyq.jar:/sdcard/framework.jar:/sdcard/fastbot-thirdpart.jar exec app_process /system/bin com.android.commands.monkey.Monkey -p ${packageName} --agent reuseq --running-minutes ${runningMinutes} --throttle ${throttle} -v -v --bugreport --output-directory ${outputDirectory} > ${remoteLogFile} 2>&1"
        // 4. 清理旧进程并执行（带重试机制）
        log.sendStepLog(StepType.INFO, "开始执行Fastbot...", "")

        boolean success = false
        int maxRetries = 3

        for (int retry = 1; retry <= maxRetries; retry++) {
            try {
                log.sendStepLog(StepType.INFO, "第 ${retry} 次尝试执行 Fastbot...", "")
                testFastbot(command)
                success = true
                break
            } catch (Exception e) {
                log.sendStepLog(StepType.WARN, "第 ${retry} 次执行失败: ${e.message}", "")
                if (retry < maxRetries) {
                    log.sendStepLog(StepType.INFO, "等待 5 秒后重试...", "")
                    Thread.sleep(5000)
                    // 每次重试前都清理环境
                    cleanupAutomationServices()
                } else {
                    throw e
                }
            }
        }

        if (!success) {
            log.sendStepLog(StepType.ERROR, "Fastbot 执行失败，已达到最大重试次数", "")
            return
        }

        androidStepHandler.log.sendStepLog(StepType.INFO, "终端ID:", "${iDevice}")
        String brand=getProductBrand()
        String model=getProductModel()

        ftp_log_path = "/root/ftp_test/logs/fastbot/${brand}_${model}_${iDevice}/${timestamp}"
        androidStepHandler.log.sendStepLog(StepType.INFO, "ftp目录保存路径:", "${ftp_log_path}")
        exec_command("mkdir -p ${ftp_log_path}")
        exec_command("adb -s ${iDevice} pull /sdcard/test ${ftp_log_path}")
        // log.sendStepLog(StepType.PASS, "Fastbot命令已异步执行，日志请在设备上查看.", "")

    } catch (Exception e) {
        log.sendStepLog(StepType.ERROR, "执行Fastbot脚本时出错", e.toString())
    }
}

checkAndPushFiles() 