name: release to github

on:
  push:
    tags:
      - "v*.*.*"

jobs:
  create:
    runs-on: ubuntu-latest
    steps:
      - uses: softprops/action-gh-release@v1
        with:
          draft: false
          generate_release_notes: true

  doc:
    needs: create
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 0
      - name: 'Get Previous tag'
        id: previoustag
        uses: "WyriHaximus/github-action-get-previous-tag@v1"
      - name: Set up JDK 17
        uses: actions/setup-java@v2
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: maven
      - name: replace version
        run: sed -i "s/SONIC_VERSION/${{ steps.previoustag.outputs.tag }}/g" src/main/docker/docker-compose*.yml
      - name: Upload yml To Github Release
        uses: xresloader/upload-to-github-release@v1.3.4
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          file: src/main/docker/docker-compose.yml;src/main/docker/docker-compose-zh.yml
          tag_name: ${{ steps.previoustag.outputs.tag }}
          update_latest_release: true
          draft: false

  release:
    needs: create
    strategy:
      matrix:
        platform: [ "windows-x86", "windows-x86_64", "macosx-arm64", "macosx-x86_64", "linux-arm64", "linux-x86", "linux-x86_64" ]
        include:
          - platform: linux-x86
            depend: linux_x86
            adb: linux
          - platform: linux-x86_64
            depend: linux_x86_64
            adb: linux
          - platform: linux-arm64
            depend: linux_arm64
            adb: linux
          - platform: macosx-x86_64
            depend: macosx_x86_64
            adb: darwin
          - platform: macosx-arm64
            depend: macosx_arm64
            adb: darwin
          - platform: windows-x86
            depend: windows_x86
            adb: windows
            tail: .exe
          - platform: windows-x86_64
            depend: windows_x86_64
            adb: windows
            tail: .exe
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 0
      - name: 'Get Previous tag'
        id: previoustag
        uses: "WyriHaximus/github-action-get-previous-tag@v1"
      - name: Set up JDK 17
        uses: actions/setup-java@v2
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: maven
      - name: replace version
        run: ver=${{ steps.previoustag.outputs.tag }} && new_ver=${ver:1} && sed -i "s/SONIC_VERSION/${new_ver}/g" pom.xml && sed -i "s/SONIC_VERSION/${{ steps.previoustag.outputs.tag }}/g" src/main/docker/docker-compose*.yml
      - name: mkdir
        run: mkdir -p sonic-agent-${{ steps.previoustag.outputs.tag }}-${{matrix.depend}}/plugins
      - name: package
        run: mvn clean package -Dplatform=${{ matrix.platform }} -Dmaven.test.skip=true
      - name: mvJar-linux86
        run: cp -r target/sonic-agent-${{ matrix.platform }}.jar sonic-agent-${{ steps.previoustag.outputs.tag }}-${{matrix.depend}}/
      - name: copy
        run: cp -r config/ mini/ sonic-agent-${{ steps.previoustag.outputs.tag }}-${{matrix.depend}}/
      - name: adb-download
        uses: robinraju/release-downloader@v1.4
        with:
          repository: SonicCloudOrg/sonic-adb-binary
          latest: true
          fileName: "*"
      - name: adb-unzip
        run: unzip platform-tools_r34.0.3-${{matrix.adb}}.zip && mv platform-tools/* sonic-agent-${{ steps.previoustag.outputs.tag }}-${{matrix.depend}}/plugins/
      - name: sas-download
        uses: robinraju/release-downloader@v1.4
        with:
          repository: SonicCloudOrg/sonic-android-supply
          latest: true
          fileName: "*"
      - name: sas-untar
        run: tar zxvf *_${{matrix.depend}}.tar.gz && rm *_${{matrix.depend}}.tar.gz && mv sas${{matrix.tail}} sonic-agent-${{ steps.previoustag.outputs.tag }}-${{matrix.depend}}/plugins/sonic-android-supply${{matrix.tail}}
      - name: sgm-download
        uses: robinraju/release-downloader@v1.4
        with:
          repository: SonicCloudOrg/sonic-go-mitmproxy
          latest: true
          fileName: "*"
      - name: sgm-untar
        run: tar zxvf *_${{matrix.depend}}.tar.gz && rm *_${{matrix.depend}}.tar.gz && mv sonic-go-mitmproxy${{matrix.tail}} sonic-agent-${{ steps.previoustag.outputs.tag }}-${{matrix.depend}}/plugins/sonic-go-mitmproxy${{matrix.tail}}
      - name: sib-download
        uses: robinraju/release-downloader@v1.4
        with:
          repository: SonicCloudOrg/sonic-ios-bridge
          latest: true
          fileName: "*"
      - name: sib-untar
        run: tar zxvf *_${{matrix.depend}}.tar.gz && rm *_${{matrix.depend}}.tar.gz && mv sib${{matrix.tail}} sonic-agent-${{ steps.previoustag.outputs.tag }}-${{matrix.depend}}/plugins/sonic-ios-bridge${{matrix.tail}}
      - name: saa-download
        uses: robinraju/release-downloader@v1.4
        with:
          repository: SonicCloudOrg/sonic-android-apk
          latest: true
          fileName: "sonic-android-apk.apk"
      - name: saa
        run: cp sonic-android-apk.apk sonic-agent-${{ steps.previoustag.outputs.tag }}-${{matrix.depend}}/plugins/
      - name: sas
        run: cp plugins/sonic-android-scrcpy.jar sonic-agent-${{ steps.previoustag.outputs.tag }}-${{matrix.depend}}/plugins/
      - name: appium1
        run: cp plugins/sonic-appium-uiautomator2-server.apk sonic-agent-${{ steps.previoustag.outputs.tag }}-${{matrix.depend}}/plugins/
      - name: appium2
        run: cp plugins/sonic-appium-uiautomator2-server-test.apk sonic-agent-${{ steps.previoustag.outputs.tag }}-${{matrix.depend}}/plugins/
      - name: zip
        uses: TheDoctor0/zip-release@0.6.2
        with:
          filename: sonic-agent-${{ steps.previoustag.outputs.tag }}-${{matrix.depend}}.zip
          directory: sonic-agent-${{ steps.previoustag.outputs.tag }}-${{matrix.depend}}
      - name: Upload Zip To Github Release
        uses: xresloader/upload-to-github-release@v1.3.4
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          file: sonic-agent-${{ steps.previoustag.outputs.tag }}-${{matrix.depend}}/sonic-agent-${{ steps.previoustag.outputs.tag }}-${{matrix.depend}}.zip
          tag_name: ${{ steps.previoustag.outputs.tag }}
          update_latest_release: true
          draft: false
