sonic:
  agent:
    # Replace with ipv4 of the agent machine | 替换为部署Agent机器的ipv4
    host: ***********
    # Replace with the port of the agent service, which can be changed by yourself | 替换为Agent服务的端口，可以自行更改
    port: 7777
    # Replace with the key of agent generated by the new front-end | 替换为前端新增Agent生成的key
    key: 5aa13292-b9a8-408c-a091-d784d1f37472
  server:
    # Change to SONIC_SERVER_HOST of server | 改成server的SONIC_SERVER_HOST
    host: ***********
    # Change to SONIC_SERVER_PORT of server | 改成server的SONIC_SERVER_PORT
    port: 3000

# The following will be migrated to the server configuration in the future | 以下未来会迁移到server配置
modules:
  ios:
    # Replace with the bundleId of wda. If there is no. xcrunner suffix, it will be automatically completed. | 替换为wda的bundleId，如果没有.xctrunner后缀会自动补全
    wda-bundle-id: com.sonic.WebDriverAgentRunner
    # (mac only) Replace with the xcode project path of wda. | 替换为wda的xcode project目录
    wda-xcode-project-path: WebdriverAgent/WebDriverAgent.xcodeproj