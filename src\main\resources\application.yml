spring:
  version: @project.version@
  application:
    name: @project.artifactId@
    des: "Agent of Sonic Cloud Real Machine Platform"
  profiles:
    active: @project.artifactId@

sonic:
  saa: 2.0.8
  sgm: 1.3.4
  sib: 1.3.20
  sas: 0.1.12
  saus: 5.7.4

logging:
  file:
    name: logs/sonic-agent.log
  logback:
    rollingpolicy:
      clean-history-on-start: true
      max-history: 3
  pattern:
    console: "%clr(%d{${LOG_DATEFORMAT_PATTERN:yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:%wEx}"
