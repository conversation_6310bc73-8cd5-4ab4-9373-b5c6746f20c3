import org.cloud.sonic.agent.bridge.android.AndroidDeviceBridgeTool
import org.cloud.sonic.agent.tests.LogUtil
import org.cloud.sonic.agent.common.interfaces.StepType
import com.android.ddmlib.IDevice
import java.text.SimpleDateFormat
import java.util.Date

/**
 * 获取设备品牌，如：OPPO
 * @return 设备品牌
 */
def getProductBrand() {
    return AndroidDeviceBridgeTool.getProperties(androidStepHandler.iDevice, "ro.product.brand") ?: ""
}

/**
 * 获取设备型号，如：MHA-AL00
 * @return 设备型号
 */
def getProductModel() {
    return AndroidDeviceBridgeTool.getProperties(androidStepHandler.iDevice, "ro.product.model") ?: ""
}


/**
 * 使用示例和测试方法
 */
def printDeviceInfo() {
    try {
        // 获取并打印设备信息
        LogUtil log = androidStepHandler.log
        Map<String, String> info = getDeviceInfo()
        log.sendStepLog(StepType.INFO, "获取设备的详细信息:", "${info}")
        log.sendStepLog(StepType.INFO, "详细信息-设备品牌:", "${info.brand}")

        // 单独获取某些信息方法
        String deviceId = getDeviceId()
        String androidVersion = getAndroidVersion()
        String brand = getProductBrand()
        String model = getProductModel()

        log.sendStepLog(StepType.PASS,
            "单独获取信息-设备:", "${brand} ${model} (${deviceId}), Android: ${androidVersion}")


    } catch (Exception e) {
        androidStepHandler.log.sendStepLog(StepType.ERROR, "获取设备的详细信息失败", e.toString())
        e.printStackTrace()
    }
}


/**
 * 执行本地PC上的command命令
 * @param args command命令参数
 * @return 命令执行结果
 */
def command(String args) {
    try {
        String command = "${args}"
        Process process = command.execute()
        process.waitFor()

        String output = process.inputStream.text
        String error = process.errorStream.text

        if (error && !error.trim().isEmpty()) {
            androidStepHandler.log.sendStepLog(StepType.WARN, "终端命令警告: ${error}", "")
        }

        return output.trim()
    } catch (Exception e) {
        androidStepHandler.log.sendStepLog(StepType.ERROR, "执行终端命令失败: ${args}", e.toString())
        return ""
    }
}

String iDevice = androidStepHandler.iDevice
androidStepHandler.log.sendStepLog(StepType.INFO, "终端ID:", "${iDevice}")
String brand=getProductBrand()
String model=getProductModel()
// 生成时间后缀，格式为 yyyyMMddHHmm
// SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmm")
// String timeStamp = dateFormat.format(new Date())
def sdf = new java.text.SimpleDateFormat("yyyyMMddHHmmss")
sdf.setTimeZone(java.util.TimeZone.getTimeZone("Asia/Shanghai"))
def timestamp = sdf.format(new Date())
ftp_log_path = "/root/ftp_test/logs/fastbot/${brand}_${model}_${iDevice}/${timeStamp}"
androidStepHandler.log.sendStepLog(StepType.INFO, "ftp目录保存路径:", "${ftp_log_path}")
command("adb -s ${iDevice} pull /sdcard/test ${ftp_log_path}")


