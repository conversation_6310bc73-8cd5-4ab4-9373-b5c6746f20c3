version: '3'
services:
  sonic-agent:
    image: "dockerproxy.com/sonicorg/sonic-agent-linux:SONIC_VERSION"
    environment:
      # Change to SONIC_SERVER_HOST and SONIC_SERVER_PORT of server | 改成server的SONIC_SERVER_HOST和SONIC_SERVER_PORT
      - SONIC_SERVER_HOST=***********
      - SONIC_SERVER_PORT=3000
      # Replace with ipv4 of the agent machine | 替换为部署Agent机器的ipv4
      - AGENT_HOST=***********
      # Replace with the port of the agent service, which can be changed by yourself | 替换为Agent服务的端口，可以自行更改
      - AGENT_PORT=7777
      # Replace with the key of agent generated by the new front-end | 替换为前端新增Agent生成的key
      - AGENT_KEY=29002272-4659-4808-a804-08ce3388b136
      # Replace with the bundleId of wda. If there is no. xcrunner suffix, it will be automatically completed. | 替换为wda的bundleId，如果没有.xctrunner后缀会自动补全
      - WDA_BUNDLE_ID=com.facebook.WebDriverAgentRunner.xctrunner
    network_mode: "host"
    privileged: true
    volumes:
      - /dev/bus/usb:/dev/bus/usb
      - /var/run/usbmuxd:/var/run/usbmuxd