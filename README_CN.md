<p align="center">
  <img width="80px" src="https://raw.githubusercontent.com/SonicCloudOrg/sonic-server/main/logo.png">
</p>
<p align="center">🎉Sonic云真机平台Agent端</p>
<p align="center">
  <a href="https://github.com/SonicCloudOrg/sonic-agent/blob/main/README.md">  
    English
  </a>
  <span>| 简体中文</span>
</p>
<p align="center">
  <a href="#">  
    <img src="https://img.shields.io/github/v/release/SonicCloudOrg/sonic-agent?include_prereleases">
  </a>
  <a href="#">  
    <img src="https://img.shields.io/badge/platform-windows|macosx|linux-success">
  </a>
</p>
<p align="center">
  <a href="#">  
    <img src="https://img.shields.io/github/commit-activity/m/SonicCloudOrg/sonic-agent">
  </a>
  <a href="#">  
    <img src="https://img.shields.io/github/downloads/SonicCloudOrg/sonic-agent/total">
  </a>
  <a href="https://github.com/SonicCloudOrg/sonic-server/blob/main/LICENSE">  
    <img src="https://img.shields.io/github/license/SonicCloudOrg/sonic-server?color=green&label=license&logo=license&logoColor=green">
  </a>
</p>

### 官方网站

[Sonic Official Website](https://soniccloudorg.github.io/)

## 背景

#### 什么是 Sonic ?

> Sonic是一个集移动设备远程控制调试与自动化测试的平台，用心为全球开发者以及测试工程师打造更好的使用体验。
>
>  如果你想参与其中，欢迎加入！💪
>
> 如果你想支持，可以给我一个star。⭐

## 部署方式

[查看这里](https://soniccloudorg.github.io/deploy/agent-deploy.html)

## 打包方式

[查看这里](https://soniccloudorg.github.io/contribute/con-agent.html)

## 赞助商

感谢所有赞助商！

[<img src="https://ceshiren.com/uploads/default/original/3X/7/0/70299922296e93e2dcab223153a928c4bfb27df9.jpeg" alt="霍格沃兹测试开发学社" width="500">](https://qrcode.testing-studio.com/f?from=sonic&url=https://ceshiren.com)

> [霍格沃兹测试开发学社](https://qrcode.testing-studio.com/f?from=sonic&url=https://ceshiren.com)
> 是业界领先的测试开发技术高端教育品牌，隶属于[测吧（北京）科技有限公司](http://qrcode.testing-studio.com/f?from=sonic&url=https://www.testing-studio.com)
> 。学院课程由一线大厂测试经理与资深测试开发专家参与研发，实战驱动。课程涵盖 web/app
>
自动化测试、接口测试、性能测试、安全测试、持续集成/持续交付/DevOps，测试左移&右移、精准测试、测试平台开发、测试管理等内容，帮助测试工程师实现测试开发技术转型。通过优秀的学社制度（奖学金、内推返学费、行业竞赛等多种方式）来实现学员、学社及用人企业的三方共赢。[进入测试开发技术能力测评!](https://qrcode.testing-studio.com/f?from=sonic&url=https://ceshiren.com/t/topic/14940)

## 开源许可协议

[License](LICENSE)

- mini文件夹二进制文件来自 [minicap](https://github.com/openstf/minicap)
  license: [Apache 2.0 License](licenses/LICENSE.minicap)
- sonic-android-scrcpy.jar 来自 sonic-android-scrcpy fork from [scrcpy](https://github.com/Genymobile/scrcpy)
  license: [Apache 2.0 License](licenses/LICENSE.scrcpy)
- sonic-appium-uiautomator2-server*.apk 来自 sonic-appium-uiautomator2-server fork
  from [appium-uiautomator2-server](https://github.com/appium/appium-uiautomator2-server)
  license: [Apache 2.0 License](licenses/LICENSE.appium-uiautomator2-server)
- sonic-go-mitmproxy 来自 sonic-go-mitmproxy fork from [go-mitmproxy](https://github.com/lqqyt2423/go-mitmproxy)
  license: [MIT License](licenses/LICENSE.go-mitmproxy)
- WebDriverAgent 来自 sonic-ios-wda fork from [WebDriverAgent](https://github.com/appium/WebDriverAgent)
  license: [BSD License](licenses/LICENSE.WebDriverAgent)
- Poco-SDK 来自 sonic-sdk-poco fork from [Poco-SDK](https://github.com/AirtestProject/Poco-SDK)
  license: [Apache 2.0 License](licenses/LICENSE.Poco-SDK)
