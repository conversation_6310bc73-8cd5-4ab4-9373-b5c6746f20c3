# 测试脚本：验证 PowerShell 命令执行修复
# 模拟 ai_test.groovy 中修复后的命令执行逻辑

Write-Host "=== 开始测试 PowerShell 命令执行修复 ===" -ForegroundColor Green

# 测试1：验证环境变量设置和读取
Write-Host "`n测试1：PowerShell 环境变量设置" -ForegroundColor Yellow
try {
    $env:TEST_VAR = "hello"
    Write-Host "设置环境变量 TEST_VAR = hello"
    Write-Host "读取环境变量: $env:TEST_VAR"
    Write-Host "测试1 - 成功" -ForegroundColor Green
} catch {
    Write-Host "测试1 - 失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试2：模拟原始的 droidrun 命令结构
Write-Host "`n测试2：模拟 droidrun 命令结构" -ForegroundColor Yellow
try {
    $env:GOOGLE_API_KEY = "AIzaSyB_qj1RZuW3A2tj5QR1yRsFjOQshAV7Qoo"
    Write-Host "设置 GOOGLE_API_KEY 环境变量"
    Write-Host "环境变量已设置: $env:GOOGLE_API_KEY"
    
    # 模拟 droidrun 命令（不实际执行）
    $mockCommand = 'droidrun "打开计算器" -d PFZ97PUO7DVOJV6T --provider GoogleGenAI --model gemini-2.5-flash --debug --vision --reasoning'
    Write-Host "模拟执行命令: $mockCommand"
    Write-Host "测试2 - 成功（模拟）" -ForegroundColor Green
} catch {
    Write-Host "测试2 - 失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试3：验证命令执行方式
Write-Host "`n测试3：验证 cmd /c powershell 执行方式" -ForegroundColor Yellow
try {
    # 模拟 Groovy 中的执行方式
    $testCommand = '$env:TEST_KEY="test_value"; echo $env:TEST_KEY'
    Write-Host "准备执行的命令: $testCommand"
    
    # 使用 cmd /c powershell 方式执行（模拟 Java Runtime.exec）
    $result = cmd /c "powershell -Command `"$testCommand`""
    Write-Host "执行结果: $result"
    Write-Host "测试3 - 成功" -ForegroundColor Green
} catch {
    Write-Host "测试3 - 失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试4：验证转义字符处理
Write-Host "`n测试4：验证转义字符处理" -ForegroundColor Yellow
try {
    $apiKey = "AIzaSyB_qj1RZuW3A2tj5QR1yRsFjOQshAV7Qoo"
    $deviceId = "PFZ97PUO7DVOJV6T"
    $droidrunCommand = "echo `"模拟 droidrun 命令执行`""
    
    # 构建类似 Groovy 中的命令
    $command = "`$env:GOOGLE_API_KEY=`"$apiKey`"; $droidrunCommand"
    Write-Host "构建的命令: $command"
    
    # 执行命令
    $result = cmd /c "powershell -Command `"$command`""
    Write-Host "执行结果: $result"
    Write-Host "测试4 - 成功" -ForegroundColor Green
} catch {
    Write-Host "测试4 - 失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "总结：修复后的 exec_command 函数应该能够正确处理 PowerShell 命令" -ForegroundColor Cyan
