<p align="center">
  <img width="80px" src="https://raw.githubusercontent.com/SonicCloudOrg/sonic-server/main/logo.png">
</p>
<p align="center">🎉Agent of Sonic Cloud Real Machine Platform</p>
<p align="center">
  <span>English |</span>
  <a href="https://github.com/SonicCloudOrg/sonic-agent/blob/main/README_CN.md">  
     简体中文
  </a>
</p>
<p align="center">
  <a href="#">  
    <img src="https://img.shields.io/github/v/release/SonicCloudOrg/sonic-agent?include_prereleases">
  </a>
  <a href="#">  
    <img src="https://img.shields.io/badge/platform-windows|macosx|linux-success">
  </a>
</p>
<p align="center">
  <a href="#">  
    <img src="https://img.shields.io/github/commit-activity/m/SonicCloudOrg/sonic-agent">
  </a>
  <a href="#">  
    <img src="https://img.shields.io/github/downloads/SonicCloudOrg/sonic-agent/total">
  </a>
  <a href="https://github.com/SonicCloudOrg/sonic-server/blob/main/LICENSE">  
    <img src="https://img.shields.io/github/license/SonicCloudOrg/sonic-server?color=green&label=license&logo=license&logoColor=green">
  </a>
</p>

### Official Website

[Sonic Official Website](https://soniccloudorg.github.io/)

## Background

#### What is sonic ?

> Sonic is a platform that integrates remote control debugging and automated testing of mobile devices, and strives to
> create a better use experience for global developers and test engineers.
>
>If you want to participate, welcome to join us! 💪
>
>If you want to support, you can give me a star. ⭐

## Deploy

[Look Here!](https://soniccloudorg.github.io/deploy/agent-deploy.html)

## Package

[Look Here!](https://soniccloudorg.github.io/contribute/con-agent.html)

## Sponsors

Thank you to all our sponsors!

[<img src="https://ceshiren.com/uploads/default/original/3X/7/0/70299922296e93e2dcab223153a928c4bfb27df9.jpeg" alt="霍格沃兹测试开发学社" width="500">](https://qrcode.testing-studio.com/f?from=sonic&url=https://ceshiren.com)

> [霍格沃兹测试开发学社](https://qrcode.testing-studio.com/f?from=sonic&url=https://ceshiren.com)
> 是业界领先的测试开发技术高端教育品牌，隶属于[测吧（北京）科技有限公司](http://qrcode.testing-studio.com/f?from=sonic&url=https://www.testing-studio.com)
> 。学院课程由一线大厂测试经理与资深测试开发专家参与研发，实战驱动。课程涵盖 web/app
>
自动化测试、接口测试、性能测试、安全测试、持续集成/持续交付/DevOps，测试左移&右移、精准测试、测试平台开发、测试管理等内容，帮助测试工程师实现测试开发技术转型。通过优秀的学社制度（奖学金、内推返学费、行业竞赛等多种方式）来实现学员、学社及用人企业的三方共赢。[进入测试开发技术能力测评!](https://qrcode.testing-studio.com/f?from=sonic&url=https://ceshiren.com/t/topic/14940)

## LICENSE

[License](LICENSE)

- Binary files in the mini folder are from [minicap](https://github.com/openstf/minicap)
  license: [Apache 2.0 License](licenses/LICENSE.minicap)
- sonic-android-scrcpy.jar in the plugins folder is from sonic-android-scrcpy fork
  from [scrcpy](https://github.com/Genymobile/scrcpy) license: [Apache 2.0 License](licenses/LICENSE.scrcpy)
- sonic-appium-uiautomator2-server*.apk in the plugins folder are from sonic-appium-uiautomator2-server fork
  from [appium-uiautomator2-server](https://github.com/appium/appium-uiautomator2-server)
  license: [Apache 2.0 License](licenses/LICENSE.appium-uiautomator2-server)
- sonic-go-mitmproxy in the plugins folder is from sonic-go-mitmproxy fork
  from [go-mitmproxy](https://github.com/lqqyt2423/go-mitmproxy) license: [MIT License](licenses/LICENSE.go-mitmproxy)
- WebDriverAgent is from sonic-ios-wda fork from [WebDriverAgent](https://github.com/appium/WebDriverAgent)
  license: [BSD License](licenses/LICENSE.WebDriverAgent)
- Poco-SDK is from sonic-sdk-poco fork from [Poco-SDK](https://github.com/AirtestProject/Poco-SDK)
  license: [Apache 2.0 License](licenses/LICENSE.Poco-SDK)
