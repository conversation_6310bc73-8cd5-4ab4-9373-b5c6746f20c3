import org.cloud.sonic.agent.bridge.android.AndroidDeviceBridgeTool
import org.cloud.sonic.agent.tests.LogUtil
import org.cloud.sonic.agent.common.interfaces.StepType
import com.android.ddmlib.IDevice

/**
 * 执行本地PC上的command命令
 * @param args command命令参数
 * @return 命令执行结果
 */
def command(String args) {
    try {
        String command = "${args}"
        Process process = command.execute()
        process.waitFor()

        String output = process.inputStream.text
        String error = process.errorStream.text

        if (error && !error.trim().isEmpty()) {
            androidStepHandler.log.sendStepLog(StepType.WARN, "终端命令警告: ${error}", "")
        }

        return output.trim()
    } catch (Exception e) {
        androidStepHandler.log.sendStepLog(StepType.ERROR, "执行终端命令失败: ${args}", e.toString())
        return ""
    }
}

command("ls")