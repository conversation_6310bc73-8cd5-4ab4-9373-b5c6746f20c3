/*
 * @Author: wj
 * @Date: 2025-07-07 10:00:00
 * @LastEditTime: 2025-07-07 10:00:00
 * @Description: 获取并打印设备信息
 */
import org.cloud.sonic.agent.bridge.android.AndroidDeviceBridgeTool
import org.cloud.sonic.agent.tests.LogUtil
import org.cloud.sonic.agent.common.interfaces.StepType
import com.android.ddmlib.IDevice



/**
 * 获取设备状态：offline | bootloader | device
 * @return 设备状态字符串
 */
def getDeviceState() {
    return androidStepHandler.iDevice.getState().toString().toLowerCase()
}

/**
 * 获取设备ID号（序列号）
 * @return 设备序列号
 */
def getDeviceId() {
    return androidStepHandler.iDevice.getSerialNumber()
}

/**
 * 获取设备中的Android版本号，如4.2.2
 * @param isBigVersion 如果为true则只获取大版本号，如1-12
 * @return Android版本号
 */
def getAndroidVersion(boolean isBigVersion = false) {
    String verText = AndroidDeviceBridgeTool.getProperties(androidStepHandler.iDevice, "ro.build.version.release")
    if (verText && isBigVersion) {
        String[] verParts = verText.split('\\.')
        if (verParts.length > 0) {
            try {
                return verParts[0]
            } catch (NumberFormatException e) {
                return verText
            }
        }
    }
    return verText ?: ""
}

/**
 * 获取设备SDK版本号，如：24
 * @return SDK版本号
 */
def getSdkVersion() {
    return AndroidDeviceBridgeTool.getProperties(androidStepHandler.iDevice, "ro.build.version.sdk") ?: ""
}

/**
 * 获取设备品牌，如：HUAWEI
 * @return 设备品牌
 */
def getProductBrand() {
    return AndroidDeviceBridgeTool.getProperties(androidStepHandler.iDevice, "ro.product.brand") ?: ""
}

/**
 * 获取设备型号，如：MHA-AL00
 * @return 设备型号
 */
def getProductModel() {
    return AndroidDeviceBridgeTool.getProperties(androidStepHandler.iDevice, "ro.product.model") ?: ""
}

/**
 * 获取产品名称（如 MHA）
 * @return 产品名称
 */
def getProductName() {
    return AndroidDeviceBridgeTool.getProperties(androidStepHandler.iDevice, "ro.product.name") ?: ""
}

/**
 * 获取主板名称
 * @return 主板名称
 */
def getProductBoard() {
    return AndroidDeviceBridgeTool.getProperties(androidStepHandler.iDevice, "ro.product.board") ?: ""
}

/**
 * 获取制造商名称
 * @return 制造商名称
 */
def getProductManufacturer() {
    return AndroidDeviceBridgeTool.getProperties(androidStepHandler.iDevice, "ro.product.manufacturer") ?: ""
}

/**
 * 获取设备代号（如 trident）
 * @return 设备代号
 */
def getProductDevice() {
    return AndroidDeviceBridgeTool.getProperties(androidStepHandler.iDevice, "ro.product.device") ?: ""
}

/**
 * 获取设备ROM名，如：MHA-AL00C00B213
 * @return ROM名称
 */
def getProductRom() {
    return AndroidDeviceBridgeTool.getProperties(androidStepHandler.iDevice, "ro.build.display.id") ?: ""
}

/**
 * 获取设备的详细信息
 * @return 包含所有设备信息的Map
 */
def getDeviceInfo() {
    return [
        "deviceId": getDeviceId(),
        "deviceState": getDeviceState(),
        "androidVersion": getAndroidVersion(),
        "androidVersionMajor": getAndroidVersion(true),
        "sdkVersion": getSdkVersion(),
        "brand": getProductBrand(),
        "model": getProductModel(),
        "productName": getProductName(),
        "board": getProductBoard(),
        "manufacturer": getProductManufacturer(),
        "device": getProductDevice(),
        "rom": getProductRom()
    ]
}



/**
 * 使用示例和测试方法
 */
def printDeviceInfo() {
    try {
        // 获取并打印设备信息
        LogUtil log = androidStepHandler.log
        Map<String, String> info = getDeviceInfo()
        log.sendStepLog(StepType.INFO, "获取设备的详细信息:", "${info}")
        log.sendStepLog(StepType.INFO, "详细信息-设备品牌:", "${info.brand}")

        // 单独获取某些信息方法
        String deviceId = getDeviceId()
        String androidVersion = getAndroidVersion()
        String brand = getProductBrand()
        String model = getProductModel()

        log.sendStepLog(StepType.PASS,
            "单独获取信息-设备:", "${brand} ${model} (${deviceId}), Android: ${androidVersion}")


    } catch (Exception e) {
        androidStepHandler.log.sendStepLog(StepType.ERROR, "获取设备的详细信息失败", e.toString())
        e.printStackTrace()
    }
}

// 执行测试
printDeviceInfo()
