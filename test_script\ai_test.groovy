/*
 * @Author: javen
 * @Date: 2025-07-04 10:00:00
 * @LastEditors: javen
 * @LastEditTime: 2025-07-04 10:00:00
 * @Description: ai测试脚本
 */
import org.cloud.sonic.agent.bridge.android.AndroidDeviceBridgeTool
import org.cloud.sonic.agent.tests.LogUtil
import org.cloud.sonic.agent.common.interfaces.StepType
import java.util.*;
import com.android.ddmlib.IShellOutputReceiver
import com.android.ddmlib.SyncService
import java.util.concurrent.TimeUnit;
import com.android.ddmlib.IDevice


/**
 * 执行本地PC上的command命令
 * @param args command命令参数
 * @return 命令执行结果
 */
def exec_command(String args) {
    try {
        String command = "${args}"
        Process process = command.execute()
        process.waitFor()

        String output = process.inputStream.text
        String error = process.errorStream.text
        androidStepHandler.log.sendStepLog(StepType.INFO, "输出日志: ${output}", "")
        if (error && !error.trim().isEmpty()) {
            androidStepHandler.log.sendStepLog(StepType.WARN, "终端命令警告: ${error}", "")
        }

        return output.trim()
    } catch (Exception e) {
        androidStepHandler.log.sendStepLog(StepType.ERROR, "执行终端命令失败: ${args}", e.toString())
        return ""
    }
}


/**
 * 清理设备上的自动化服务和相关进程
 */
def cleanupAutomationServices() {
    LogUtil log = androidStepHandler.log
    def iDevice = androidStepHandler.iDevice

    log.sendStepLog(StepType.INFO, "开始清理设备上的自动化服务...", "")

    try {
        // 1. 杀死所有 monkey 相关进程
        AndroidDeviceBridgeTool.executeCommand(iDevice, "pkill -f monkey")
        AndroidDeviceBridgeTool.executeCommand(iDevice, "pkill -f fastbot")
        AndroidDeviceBridgeTool.executeCommand(iDevice, "pkill -f app_process")

        // 2. 清理 UiAutomation 相关进程
        AndroidDeviceBridgeTool.executeCommand(iDevice, "pkill -f uiautomator")

        // 3. 重启 accessibility 服务
        AndroidDeviceBridgeTool.executeCommand(iDevice, "settings put secure accessibility_enabled 0")
        Thread.sleep(1000)
        AndroidDeviceBridgeTool.executeCommand(iDevice, "settings put secure accessibility_enabled 1")

        // 4. 等待服务重启
        Thread.sleep(2000)

        log.sendStepLog(StepType.PASS, "自动化服务清理完成", "")

    } catch (Exception e) {
        log.sendStepLog(StepType.WARN, "清理自动化服务时出现异常", e.toString())
    }
}


def checkAndPushFiles() {
    // 检查并同步Fastbot所需文件
    LogUtil log = androidStepHandler.log
    def iDevice = androidStepHandler.iDevice
    // 首先强制清理环境
    cleanupAutomationServices()
    try {
        String command = '$env:GOOGLE_API_KEY="AIzaSyB_qj1RZuW3A2tj5QR1yRsFjOQshAV7Qoo"; droidrun "打开计算器" -d PFZ97PUO7DVOJV6T --provider GoogleGenAI --model gemini-2.5-flash --debug --vision --reasoning'
        // 4. 清理旧进程并执行（带重试机制）
        log.sendStepLog(StepType.INFO, "开始执行...", "")
        exec_command(command)

    } catch (Exception e) {
        log.sendStepLog(StepType.ERROR, "执行脚本时出错", e.toString())
    }

    // 重新启动 UIA 服务器和驱动
    try {
        int port = AndroidDeviceBridgeTool.startUiaServer(androidStepHandler.iDevice);
        androidStepHandler.startAndroidDriver(androidStepHandler.iDevice, port)
    } catch (Exception e) {
        log.sendStepLog(StepType.WARN, "重启驱动时出现异常", e.toString())
    }
}

checkAndPushFiles() 


