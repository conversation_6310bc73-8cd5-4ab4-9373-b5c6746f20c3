/*
 * @Author: javen
 * @Date: 2025-07-04 10:00:00
 * @LastEditors: javen
 * @LastEditTime: 2025-07-04 10:00:00
 * @Description: ai测试脚本
 */
import org.cloud.sonic.agent.bridge.android.AndroidDeviceBridgeTool
import org.cloud.sonic.agent.tests.LogUtil
import org.cloud.sonic.agent.common.interfaces.StepType
import java.util.*;
import com.android.ddmlib.IShellOutputReceiver
import com.android.ddmlib.SyncService
import java.util.concurrent.TimeUnit;
import com.android.ddmlib.IDevice
import java.io.StringWriter
import java.io.PrintWriter


/**
 * 执行本地PC上的command命令
 * @param args command命令参数
 * @return 命令执行结果
 */
def exec_command(String args) {
    try {
        String system = System.getProperty("os.name").toLowerCase()
        Process process = null

        androidStepHandler.log.sendStepLog(StepType.INFO, "准备执行命令: ${args}", "")
        androidStepHandler.log.sendStepLog(StepType.INFO, "检测到操作系统: ${system}", "")

        // 根据操作系统选择合适的命令执行方式
        if (system.contains("win")) {
            // Windows 系统：检查是否为 PowerShell 命令
            if (args.contains('$env:') || args.contains('powershell')) {
                // PowerShell 命令：使用 cmd /c powershell 方式执行
                String powershellCommand = "powershell -Command \"${args}\""
                androidStepHandler.log.sendStepLog(StepType.INFO, "使用 PowerShell 执行: ${powershellCommand}", "")
                process = Runtime.getRuntime().exec(["cmd", "/c", powershellCommand] as String[])
            } else {
                // 普通 Windows 命令
                androidStepHandler.log.sendStepLog(StepType.INFO, "使用 CMD 执行: ${args}", "")
                process = Runtime.getRuntime().exec(["cmd", "/c", args] as String[])
            }
        } else if (system.contains("linux") || system.contains("mac")) {
            // Linux/Mac 系统
            androidStepHandler.log.sendStepLog(StepType.INFO, "使用 Shell 执行: ${args}", "")
            process = Runtime.getRuntime().exec(["sh", "-c", args] as String[])
        } else {
            throw new RuntimeException("不支持的操作系统: ${system}")
        }

        // 使用轮询方式等待进程完成，同时检查停止信号
        boolean processCompleted = false
        boolean forceStopped = false

        while (!processCompleted && !forceStopped) {
            try {
                // 等待最多1秒
                processCompleted = process.waitFor(1, TimeUnit.SECONDS)

                if (!processCompleted) {
                    // 检查是否收到停止信号
                    if (Thread.currentThread() instanceof org.cloud.sonic.agent.tests.RunStepThread) {
                        org.cloud.sonic.agent.tests.RunStepThread runStepThread = (org.cloud.sonic.agent.tests.RunStepThread) Thread.currentThread()
                        if (runStepThread.isStopped()) {
                            androidStepHandler.log.sendStepLog(StepType.WARN, "检测到强制终止信号，正在停止命令执行...", "")
                            process.destroyForcibly()
                            forceStopped = true
                            androidStepHandler.log.sendStepLog(StepType.WARN, "命令已被强制终止", "")
                            return "命令被强制终止"
                        }
                    }
                }
            } catch (InterruptedException e) {
                androidStepHandler.log.sendStepLog(StepType.WARN, "命令执行被中断", "")
                process.destroyForcibly()
                return "命令被中断"
            }
        }

        String output = process.inputStream.text
        String error = process.errorStream.text

        androidStepHandler.log.sendStepLog(StepType.INFO, "命令执行完成，退出码: ${process.exitValue()}", "")
        androidStepHandler.log.sendStepLog(StepType.INFO, "输出日志: ${output}", "")

        if (error && !error.trim().isEmpty()) {
            androidStepHandler.log.sendStepLog(StepType.WARN, "终端命令警告: ${error}", "")
        }

        return output.trim()
    } catch (Exception e) {
        androidStepHandler.log.sendStepLog(StepType.ERROR, "执行终端命令失败: ${args}", e.toString())
        return ""
    }
}


/**
 * 清理设备上的自动化服务和相关进程
 */
def cleanupAutomationServices() {
    LogUtil log = androidStepHandler.log
    def iDevice = androidStepHandler.iDevice

    log.sendStepLog(StepType.INFO, "开始清理设备上的自动化服务...", "")
    log.sendStepLog(StepType.INFO, "设备序列号: ${iDevice.getSerialNumber()}", "")

    try {
        // 1. 杀死所有 monkey 相关进程
        log.sendStepLog(StepType.INFO, "正在清理 monkey 相关进程...", "")
        String monkeyResult1 = AndroidDeviceBridgeTool.executeCommand(iDevice, "pkill -f monkey")
        String monkeyResult2 = AndroidDeviceBridgeTool.executeCommand(iDevice, "pkill -f fastbot")
        String monkeyResult3 = AndroidDeviceBridgeTool.executeCommand(iDevice, "pkill -f app_process")

        if (monkeyResult1 || monkeyResult2 || monkeyResult3) {
            log.sendStepLog(StepType.INFO, "monkey 进程清理结果: ${monkeyResult1} | ${monkeyResult2} | ${monkeyResult3}", "")
        }

        // 2. 清理 UiAutomation 相关进程
        log.sendStepLog(StepType.INFO, "正在清理 UiAutomation 相关进程...", "")
        String uiResult = AndroidDeviceBridgeTool.executeCommand(iDevice, "pkill -f uiautomator")
        if (uiResult) {
            log.sendStepLog(StepType.INFO, "UiAutomation 进程清理结果: ${uiResult}", "")
        }

        // 3. 重启 accessibility 服务
        log.sendStepLog(StepType.INFO, "正在重启 accessibility 服务...", "")
        AndroidDeviceBridgeTool.executeCommand(iDevice, "settings put secure accessibility_enabled 0")
        Thread.sleep(1000)
        AndroidDeviceBridgeTool.executeCommand(iDevice, "settings put secure accessibility_enabled 1")

        // 4. 等待服务重启
        log.sendStepLog(StepType.INFO, "等待服务重启完成...", "")
        Thread.sleep(2000)

        log.sendStepLog(StepType.PASS, "自动化服务清理完成", "")

    } catch (Exception e) {
        log.sendStepLog(StepType.ERROR, "清理自动化服务时出现严重异常", e.toString())
        log.sendStepLog(StepType.WARN, "异常详情: ${e.getMessage()}", "")
        // 记录堆栈跟踪以便调试
        StringWriter sw = new StringWriter()
        PrintWriter pw = new PrintWriter(sw)
        e.printStackTrace(pw)
        log.sendStepLog(StepType.WARN, "堆栈跟踪: ${sw.toString()}", "")
    }
}


def checkAndPushFiles() {
    // 检查并同步Fastbot所需文件
    LogUtil log = androidStepHandler.log
    def iDevice = androidStepHandler.iDevice
    // 首先强制清理环境
    cleanupAutomationServices()
    try {
        // 构建 PowerShell 命令：设置环境变量并执行 droidrun
        String apiKey = "AIzaSyB_qj1RZuW3A2tj5QR1yRsFjOQshAV7Qoo"
        String deviceId = "PFZ97PUO7DVOJV6T"
        String droidrunCommand = "droidrun '打开计算器' -d ${deviceId} --provider GoogleGenAI --model gemini-2.5-flash --debug --vision --reasoning"

        // 使用 PowerShell 语法设置环境变量并执行命令（修复引号转义问题）
        String command = "\$env:GOOGLE_API_KEY='${apiKey}'; ${droidrunCommand}"

        log.sendStepLog(StepType.INFO, "准备执行 AI 自动化命令...", "")
        log.sendStepLog(StepType.INFO, "设备ID: ${deviceId}", "")
        log.sendStepLog(StepType.INFO, "开始执行...", "")

        String result = exec_command(command)

        if (result && !result.trim().isEmpty()) {
            log.sendStepLog(StepType.PASS, "AI 自动化命令执行完成", result)
        } else {
            log.sendStepLog(StepType.WARN, "AI 自动化命令执行完成，但无输出结果", "")
        }

    } catch (Exception e) {
        log.sendStepLog(StepType.ERROR, "执行 AI 自动化脚本时出错", e.toString())
    }

    // 重新启动 UIA 服务器和驱动
    try {
        log.sendStepLog(StepType.INFO, "重新启动 UIA 服务器和驱动...", "")
        int port = AndroidDeviceBridgeTool.startUiaServer(androidStepHandler.iDevice);
        androidStepHandler.startAndroidDriver(androidStepHandler.iDevice, port)
        log.sendStepLog(StepType.PASS, "UIA 服务器和驱动重启成功", "")
    } catch (Exception e) {
        log.sendStepLog(StepType.WARN, "重启驱动时出现异常", e.toString())
    }
}

checkAndPushFiles() 


